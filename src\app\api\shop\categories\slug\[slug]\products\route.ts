import { NextRequest, NextResponse } from 'next/server';
import { ensureValidSlug } from '@/utils/slugUtils';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Category by Slug API - Base URL:', API_BASE_URL);
console.log('Category by Slug API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching category products by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const rawSlug = params.slug;
    const slug = ensureValidSlug(rawSlug);
    const url = new URL(request.url);
    const searchParams = url.searchParams;

    // Get pagination parameters
    const page = searchParams.get('page') || '1';
    const limit = searchParams.get('limit') || '20';

    console.log(`Category by Slug API - Raw slug: ${rawSlug}, Normalized slug: ${slug}`);
    console.log(`Category by Slug API - Fetching products for category slug: ${slug}`);
    console.log(`Category by Slug API - Page: ${page}, Limit: ${limit}`);

    // Construct the backend URL with pagination
    const queryParams = new URLSearchParams();
    queryParams.append('page', page);
    queryParams.append('limit', limit);

    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/categories/slug/${slug}/products?${queryParams.toString()}`;

    console.log(`Category by Slug API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache control for ISR
      next: { revalidate: 86400 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch products for category ${slug}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Category by Slug API - Response received for ${slug}:`, {
      totalProducts: data.pagination?.total || data.data?.length || 0
    });

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Category by Slug API error for ${params.slug}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch category products' },
      { status: 500 }
    );
  }
}
