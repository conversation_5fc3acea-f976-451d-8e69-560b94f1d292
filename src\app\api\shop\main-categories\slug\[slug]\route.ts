import { NextRequest, NextResponse } from 'next/server';

// Get the API base URL and path prefix from environment variables
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

// Log the API configuration to help with debugging
console.log('Main Category by Slug API - Base URL:', API_BASE_URL);
console.log('Main Category by Slug API - Shop Path Prefix:', API_SHOP_PATH_PREFIX);

// GET handler for fetching a single main category by slug
export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const slug = params.slug;

    console.log(`Main Category by Slug API - Fetching main category: ${slug}`);

    // Construct the backend URL
    const backendUrl = `${API_BASE_URL}${API_SHOP_PATH_PREFIX}/main-categories/slug/${slug}`;

    console.log(`Main Category by Slug API - Calling backend URL: ${backendUrl}`);

    const response = await fetch(backendUrl, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      },
      // Add cache control for ISR
      next: { revalidate: 86400 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Backend response error: ${response.status} ${response.statusText}`);
      return NextResponse.json(
        { error: `Failed to fetch main category ${slug}` },
        { status: response.status }
      );
    }

    const data = await response.json();
    console.log(`Main Category by Slug API - Response received for ${slug}`);

    return NextResponse.json(data);
  } catch (error) {
    console.error(`Main Category by Slug API error for ${params.slug}:`, error);
    return NextResponse.json(
      { error: 'Failed to fetch main category' },
      { status: 500 }
    );
  }
}
