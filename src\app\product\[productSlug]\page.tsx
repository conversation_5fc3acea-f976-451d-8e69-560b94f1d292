import { Metadata } from "next";
import { notFound } from "next/navigation";
import ProductDetailPageClient from "@/components/shop/ProductDetailPageClient";
import { ensureValidSlug } from "@/utils/slugUtils";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  images?: string[];
  price: number;
  salePrice?: number;
  inStock: boolean;
  shortDescription?: string;
  description?: string;
  tags?: string[];
  variants?: any[];
  created_at: string;
}

interface ProductPageProps {
  params: {
    productSlug: string;
  };
}

// Server-side data fetching
async function getProductData(productSlug: string) {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NEXT_PUBLIC_API_BASE_URL;
    
    // Fetch product data
    const response = await fetch(`${baseUrl}/api/shop/products/slug/${productSlug}`, {
      next: { revalidate: 86400 } // 24 hours cache
    });
    
    if (!response.ok) {
      console.error(`Failed to fetch product data for ${productSlug}: ${response.status} ${response.statusText}`);
      return null;
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching product data:', error);
    return null;
  }
}

// Generate static params for all products
export async function generateStaticParams() {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NODE_ENV === 'production' 
      ? 'https://cocojojo.com' 
      : 'http://localhost:3000';
    
    // Get all products for static generation
    const response = await fetch(`${baseUrl}/api/shop/products`);
    
    if (!response.ok) {
      console.error(`Failed to fetch products for static generation: ${response.status} ${response.statusText}`);
      return [];
    }
    
    const data = await response.json();
    const products = data.data || [];
    
    return products.map((product: Product) => ({
      productSlug: product.slug,
    }));
  } catch (error) {
    console.error('Error generating static params for products:', error);
    return [];
  }
}

// Generate dynamic metadata
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const normalizedSlug = ensureValidSlug(params.productSlug);
  const data = await getProductData(normalizedSlug);
  
  if (!data) {
    return {
      title: 'Product Not Found | CocoJojo',
      description: 'The requested product could not be found.',
    };
  }

  const product = data.data || data;
  const currentPrice = product.salePrice || product.price;
  
  return {
    title: `${product.name} - Premium Organic Beauty | CocoJojo`,
    description: product.shortDescription || product.description || `Shop ${product.name} - premium organic beauty product with natural ingredients. ${product.inStock ? 'In stock' : 'Currently unavailable'} - $${currentPrice.toFixed(2)}`,
    keywords: `${product.name}, organic beauty, natural skincare, ${product.tags?.join(', ') || 'premium cosmetics'}`,
    openGraph: {
      title: `${product.name} - Premium Organic Beauty | CocoJojo`,
      description: product.shortDescription || product.description || `Shop ${product.name} - premium organic beauty product`,
      type: "website",
      url: `/product/${normalizedSlug}`,
      images: [
        {
          url: product.imageUrl,
          width: 800,
          height: 800,
          alt: product.name,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${product.name} - Premium Organic Beauty | CocoJojo`,
      description: product.shortDescription || product.description || `Shop ${product.name}`,
      images: [product.imageUrl],
    },
    alternates: {
      canonical: `/product/${normalizedSlug}`,
    },
  };
}

export default async function ProductPage({ params }: ProductPageProps) {
  // Normalize the slug parameter
  const normalizedSlug = ensureValidSlug(params.productSlug);
  
  // Fetch data server-side for SEO
  const data = await getProductData(normalizedSlug);
  
  if (!data) {
    notFound();
  }

  const product = data.data || data;

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            "name": product.name,
            "description": product.shortDescription || product.description,
            "image": [product.imageUrl, ...(product.images || [])],
            "sku": product.sku,
            "offers": {
              "@type": "Offer",
              "price": product.salePrice || product.price,
              "priceCurrency": "USD",
              "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
              "url": `https://cocojojo.com/product/${normalizedSlug}`
            },
            "brand": {
              "@type": "Brand",
              "name": "CocoJojo"
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://cocojojo.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": "https://cocojojo.com/shop"
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": product.name,
                  "item": `https://cocojojo.com/product/${normalizedSlug}`
                }
              ]
            }
          })
        }}
      />

      <ProductDetailPageClient 
        initialProduct={product}
        productSlug={normalizedSlug}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 86400; // 24 hours
