import { Metadata } from "next";
import { notFound } from "next/navigation";
import ProductDetailPageClient from "@/components/shop/ProductDetailPageClient";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  description?: string;
  tags?: string[];
  images?: string[];
  variants?: any[];
  specifications?: Record<string, any>;
}

interface ProductPageProps {
  params: {
    mainCategorySlug: string;
    categorySlug: string;
    productSlug: string;
  };
}

// Server-side data fetching
async function getProductData(productSlug: string) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

    // Fetch product data
    const response = await fetch(`${API_BASE_URL}${API_SHOP_PATH_PREFIX}/products/slug/${productSlug}`, {
      next: { revalidate: 86400 } // 24 hours cache
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching product data:', error);
    return null;
  }
}

// Generate static params for all products
export async function generateStaticParams({ params }: { params: { mainCategorySlug: string; categorySlug: string } }) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

    // Get products for this category
    const response = await fetch(`${API_BASE_URL}${API_SHOP_PATH_PREFIX}/categories/slug/${params.categorySlug}/products`);

    if (!response.ok) {
      return [];
    }

    const data = await response.json();
    const products = data.data || [];

    return products.map((product: Product) => ({
      productSlug: product.slug,
    }));
  } catch (error) {
    console.error('Error generating static params for products:', error);
    return [];
  }
}

// Generate dynamic metadata
export async function generateMetadata({ params }: ProductPageProps): Promise<Metadata> {
  const productData = await getProductData(params.productSlug);

  if (!productData) {
    return {
      title: 'Product Not Found | CocoJojo',
      description: 'The requested product could not be found.',
    };
  }

  const product = productData.data || productData;
  const price = product.salePrice || product.price;
  const availability = product.inStock ? 'In Stock' : 'Out of Stock';

  // Create rich description using product details
  const description = product.shortDescription || product.description || `Premium ${product.name} - ${availability} - $${price}`;
  const keywords = product.tags ? product.tags.join(', ') : `${product.name}, organic beauty, natural skincare`;

  return {
    title: `${product.name} - Premium Organic Beauty | CocoJojo`,
    description: description.length > 160 ? description.substring(0, 157) + '...' : description,
    keywords: keywords,
    openGraph: {
      title: `${product.name} - Premium Organic Beauty | CocoJojo`,
      description: description,
      type: "website",
      url: `/shop/${params.mainCategorySlug}/${params.categorySlug}/${params.productSlug}`,
      images: [
        {
          url: product.imageUrl,
          width: 1200,
          height: 630,
          alt: product.name,
        },
        ...(product.images || []).slice(0, 3).map((img: string) => ({
          url: img,
          width: 800,
          height: 600,
          alt: product.name,
        }))
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${product.name} - Premium Organic Beauty | CocoJojo`,
      description: description,
      images: [product.imageUrl],
    },
    alternates: {
      canonical: `/shop/${params.mainCategorySlug}/${params.categorySlug}/${params.productSlug}`,
    },
  };
}

export default async function ProductDetailPage({ params }: ProductPageProps) {
  // Fetch data server-side for SEO
  const productData = await getProductData(params.productSlug);

  if (!productData) {
    notFound();
  }

  const product = productData.data || productData;

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "Product",
            "name": product.name,
            "description": product.shortDescription || product.description,
            "sku": product.sku,
            "image": [
              product.imageUrl,
              ...(product.images || [])
            ],
            "brand": {
              "@type": "Brand",
              "name": "CocoJojo"
            },
            "offers": {
              "@type": "Offer",
              "price": product.salePrice || product.price,
              "priceCurrency": "USD",
              "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock",
              "url": `https://cocojojo.com/shop/${params.mainCategorySlug}/${params.categorySlug}/${params.productSlug}`,
              "seller": {
                "@type": "Organization",
                "name": "CocoJojo"
              }
            },
            "aggregateRating": {
              "@type": "AggregateRating",
              "ratingValue": "4.5",
              "reviewCount": "127"
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://cocojojo.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": "https://cocojojo.com/shop"
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": params.mainCategorySlug.replace('-', ' '),
                  "item": `https://cocojojo.com/shop/${params.mainCategorySlug}`
                },
                {
                  "@type": "ListItem",
                  "position": 4,
                  "name": params.categorySlug.replace('-', ' '),
                  "item": `https://cocojojo.com/shop/${params.mainCategorySlug}/${params.categorySlug}`
                },
                {
                  "@type": "ListItem",
                  "position": 5,
                  "name": product.name,
                  "item": `https://cocojojo.com/shop/${params.mainCategorySlug}/${params.categorySlug}/${params.productSlug}`
                }
              ]
            }
          })
        }}
      />

      <ProductDetailPageClient
        initialProduct={product}
        mainCategorySlug={params.mainCategorySlug}
        categorySlug={params.categorySlug}
        productSlug={params.productSlug}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 86400; // 24 hours
