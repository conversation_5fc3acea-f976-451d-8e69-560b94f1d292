import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import CategoryPageClient from "@/components/shop/CategoryPageClient";

// Types
interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: string[];
}

interface CategoryPageProps {
  params: {
    mainCategorySlug: string;
    categorySlug: string;
  };
}

// Server-side data fetching
async function getCategoryData(categorySlug: string) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

    // Fetch category data and products
    const response = await fetch(`${API_BASE_URL}${API_SHOP_PATH_PREFIX}/categories/slug/${categorySlug}/products`, {
      next: { revalidate: 86400 } // 24 hours cache
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching category data:', error);
    return null;
  }
}

// Get main category data for breadcrumbs
async function getMainCategoryData(mainCategorySlug: string) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

    const response = await fetch(`${API_BASE_URL}${API_SHOP_PATH_PREFIX}/main-categories/slug/${mainCategorySlug}`, {
      next: { revalidate: 86400 } // 24 hours cache
    });

    if (!response.ok) {
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching main category data:', error);
    return null;
  }
}

// Generate static params for all categories
export async function generateStaticParams({ params }: { params: { mainCategorySlug: string } }) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';

    // Get categories for this main category
    const response = await fetch(`${API_BASE_URL}${API_SHOP_PATH_PREFIX}/main-categories/slug/${params.mainCategorySlug}/products`);

    if (!response.ok) {
      return [];
    }

    const data = await response.json();
    const categories = data.categories || [];

    return categories.map((category: Category) => ({
      categorySlug: category.slug,
    }));
  } catch (error) {
    console.error('Error generating static params for categories:', error);
    return [];
  }
}

// Generate dynamic metadata
export async function generateMetadata({ params }: CategoryPageProps): Promise<Metadata> {
  const [categoryData, mainCategoryData] = await Promise.all([
    getCategoryData(params.categorySlug),
    getMainCategoryData(params.mainCategorySlug)
  ]);

  if (!categoryData) {
    return {
      title: 'Category Not Found | CocoJojo',
      description: 'The requested category could not be found.',
    };
  }

  const category = categoryData.category || { name: 'Category', slug: params.categorySlug };
  const mainCategory = mainCategoryData?.data || { name: 'Category' };
  const products = categoryData.data || [];
  const productCount = categoryData.pagination?.total || products.length;

  // Extract tags from products for keywords
  const allTags = products.flatMap((product: Product) => product.tags || []);
  const uniqueTagsSet = new Set(allTags);
  const uniqueTags = Array.from(uniqueTagsSet).slice(0, 10).join(', ');

  return {
    title: `${category.name} - ${mainCategory.name} | CocoJojo`,
    description: `Shop ${productCount} premium ${category.name.toLowerCase()} products in our ${mainCategory.name.toLowerCase()} collection. ${uniqueTags ? `Including ${uniqueTags}.` : ''} Free shipping on orders over $50.`,
    keywords: `${category.name.toLowerCase()}, ${mainCategory.name.toLowerCase()}, organic beauty, natural skincare, ${uniqueTags}`,
    openGraph: {
      title: `${category.name} - ${mainCategory.name} | CocoJojo`,
      description: `Shop ${productCount} premium ${category.name.toLowerCase()} products in our ${mainCategory.name.toLowerCase()} collection.`,
      type: "website",
      url: `/shop/${params.mainCategorySlug}/${params.categorySlug}`,
      images: [
        {
          url: category.imageUrl || "/images/category-og.jpg",
          width: 1200,
          height: 630,
          alt: `${category.name} - CocoJojo`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${category.name} - ${mainCategory.name} | CocoJojo`,
      description: `Shop ${productCount} premium ${category.name.toLowerCase()} products.`,
    },
    alternates: {
      canonical: `/shop/${params.mainCategorySlug}/${params.categorySlug}`,
    },
  };
}

export default async function CategoryPage({ params }: CategoryPageProps) {
  // Fetch data server-side for SEO
  const [categoryData, mainCategoryData] = await Promise.all([
    getCategoryData(params.categorySlug),
    getMainCategoryData(params.mainCategorySlug)
  ]);

  if (!categoryData) {
    notFound();
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": categoryData.category?.name || "Category",
            "description": `Premium organic ${categoryData.category?.name?.toLowerCase() || 'beauty'} products`,
            "url": `https://cocojojo.com/shop/${params.mainCategorySlug}/${params.categorySlug}`,
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": categoryData.pagination?.total || categoryData.data?.length || 0,
              "itemListElement": (categoryData.data || []).slice(0, 10).map((product: Product, index: number) => ({
                "@type": "Product",
                "position": index + 1,
                "name": product.name,
                "url": `https://cocojojo.com/shop/${params.mainCategorySlug}/${params.categorySlug}/${product.slug}`,
                "image": product.imageUrl,
                "description": product.shortDescription,
                "offers": {
                  "@type": "Offer",
                  "price": product.salePrice || product.price,
                  "priceCurrency": "USD",
                  "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
                }
              }))
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://cocojojo.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": "https://cocojojo.com/shop"
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": mainCategoryData?.data?.name || "Category",
                  "item": `https://cocojojo.com/shop/${params.mainCategorySlug}`
                },
                {
                  "@type": "ListItem",
                  "position": 4,
                  "name": categoryData.category?.name || "Subcategory",
                  "item": `https://cocojojo.com/shop/${params.mainCategorySlug}/${params.categorySlug}`
                }
              ]
            }
          })
        }}
      />

      <CategoryPageClient
        initialData={categoryData}
        mainCategoryData={mainCategoryData?.data}
        mainCategorySlug={params.mainCategorySlug}
        categorySlug={params.categorySlug}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 86400; // 24 hours
