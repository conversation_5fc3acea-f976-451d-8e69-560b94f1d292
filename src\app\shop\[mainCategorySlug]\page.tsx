import { <PERSON>ada<PERSON> } from "next";
import { notFound } from "next/navigation";
import MainCategoryPageClient from "@/components/shop/MainCategoryPageClient";

// Types
interface MainCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: string[];
}

interface MainCategoryPageProps {
  params: {
    mainCategorySlug: string;
  };
}

// Server-side data fetching
async function getMainCategoryData(slug: string) {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';
    
    // Fetch main category data and products
    const response = await fetch(`${API_BASE_URL}${API_SHOP_PATH_PREFIX}/main-categories/slug/${slug}/products`, {
      next: { revalidate: 86400 } // 24 hours cache
    });
    
    if (!response.ok) {
      return null;
    }
    
    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching main category data:', error);
    return null;
  }
}

// Generate static params for all main categories
export async function generateStaticParams() {
  try {
    const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:3300';
    const API_SHOP_PATH_PREFIX = process.env.NEXT_PUBLIC_API_SHOP_PATH_PREFIX || '/api/shop';
    
    const response = await fetch(`${API_BASE_URL}${API_SHOP_PATH_PREFIX}/main-categories`);
    
    if (!response.ok) {
      return [];
    }
    
    const data = await response.json();
    const mainCategories = data.data || [];
    
    return mainCategories.map((category: MainCategory) => ({
      mainCategorySlug: category.slug,
    }));
  } catch (error) {
    console.error('Error generating static params for main categories:', error);
    return [];
  }
}

// Generate dynamic metadata
export async function generateMetadata({ params }: MainCategoryPageProps): Promise<Metadata> {
  const data = await getMainCategoryData(params.mainCategorySlug);
  
  if (!data) {
    return {
      title: 'Category Not Found | CocoJojo',
      description: 'The requested category could not be found.',
    };
  }

  const mainCategory = data.mainCategory || { name: 'Category', slug: params.mainCategorySlug };
  const products = data.data || [];
  const categories = data.categories || [];
  
  const categoryNames = categories.map((cat: Category) => cat.name).join(', ');
  const productCount = data.pagination?.total || products.length;
  
  return {
    title: `${mainCategory.name} - Premium Organic Beauty Products | CocoJojo`,
    description: `Discover our ${mainCategory.name.toLowerCase()} collection with ${productCount} premium organic products. Shop ${categoryNames} and more with fast shipping.`,
    keywords: `${mainCategory.name.toLowerCase()}, organic beauty, natural skincare, ${categoryNames.toLowerCase()}, premium cosmetics`,
    openGraph: {
      title: `${mainCategory.name} - Premium Organic Beauty Products | CocoJojo`,
      description: `Discover our ${mainCategory.name.toLowerCase()} collection with ${productCount} premium organic products. Shop ${categoryNames} and more with fast shipping.`,
      type: "website",
      url: `/shop/${params.mainCategorySlug}`,
      images: [
        {
          url: mainCategory.imageUrl || "/images/category-og.jpg",
          width: 1200,
          height: 630,
          alt: `${mainCategory.name} - CocoJojo`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${mainCategory.name} - Premium Organic Beauty Products | CocoJojo`,
      description: `Discover our ${mainCategory.name.toLowerCase()} collection with ${productCount} premium organic products.`,
    },
    alternates: {
      canonical: `/shop/${params.mainCategorySlug}`,
    },
  };
}

export default async function MainCategoryPage({ params }: MainCategoryPageProps) {
  // Fetch data server-side for SEO
  const data = await getMainCategoryData(params.mainCategorySlug);
  
  if (!data) {
    notFound();
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": data.mainCategory?.name || "Category",
            "description": `Premium organic ${data.mainCategory?.name?.toLowerCase() || 'beauty'} products`,
            "url": `https://cocojojo.com/shop/${params.mainCategorySlug}`,
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": data.pagination?.total || data.data?.length || 0,
              "itemListElement": (data.data || []).slice(0, 10).map((product: Product, index: number) => ({
                "@type": "Product",
                "position": index + 1,
                "name": product.name,
                "url": `https://cocojojo.com/shop/${params.mainCategorySlug}/product/${product.slug}`,
                "image": product.imageUrl,
                "offers": {
                  "@type": "Offer",
                  "price": product.salePrice || product.price,
                  "priceCurrency": "USD",
                  "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
                }
              }))
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://cocojojo.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": "https://cocojojo.com/shop"
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": data.mainCategory?.name || "Category",
                  "item": `https://cocojojo.com/shop/${params.mainCategorySlug}`
                }
              ]
            }
          })
        }}
      />
      
      <MainCategoryPageClient 
        initialData={data}
        mainCategorySlug={params.mainCategorySlug}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 86400; // 24 hours
