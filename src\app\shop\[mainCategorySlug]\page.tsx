import { Metadata } from "next";
import { notFound } from "next/navigation";
import MainCategoryPageClient from "@/components/shop/MainCategoryPageClient";
import { ensureValidSlug } from "@/utils/slugUtils";

// Types
interface MainCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Category {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
  count: number;
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: string[];
}

interface MainCategoryPageProps {
  params: {
    mainCategorySlug: string;
  };
}

// Server-side data fetching
async function getMainCategoryData(slug: string, page: number = 1, limit: number = 20) {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://cocojojo.com'
      : 'http://localhost:3000';

    // Fetch main category data and products with pagination
    const queryParams = new URLSearchParams();
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());

    const response = await fetch(`${baseUrl}/api/shop/${slug}?${queryParams.toString()}`, {
      next: { revalidate: 86400 } // 24 hours cache
    });

    if (!response.ok) {
      console.error(`Failed to fetch main category data for ${slug}: ${response.status} ${response.statusText}`);
      return null;
    }

    const data = await response.json();
    return data;
  } catch (error) {
    console.error('Error fetching main category data:', error);
    return null;
  }
}

// Generate static params for all main categories
export async function generateStaticParams() {
  try {
    // Use Next.js API routes instead of direct external API calls
    const baseUrl = process.env.NODE_ENV === 'production'
      ? 'https://cocojojo.com'
      : 'http://localhost:3000';

    const response = await fetch(`${baseUrl}/api/shop/main-categories`);

    if (!response.ok) {
      console.error(`Failed to fetch main categories: ${response.status} ${response.statusText}`);
      return [];
    }

    const data = await response.json();
    const mainCategories = data.data || [];

    return mainCategories.map((category: MainCategory) => ({
      mainCategorySlug: category.slug,
    }));
  } catch (error) {
    console.error('Error generating static params for main categories:', error);
    return [];
  }
}

// Generate dynamic metadata
export async function generateMetadata({ params }: MainCategoryPageProps): Promise<Metadata> {
  const normalizedSlug = ensureValidSlug(params.mainCategorySlug);
  const data = await getMainCategoryData(normalizedSlug);
  
  if (!data) {
    return {
      title: 'Category Not Found | CocoJojo',
      description: 'The requested category could not be found.',
    };
  }

  const mainCategory = data.mainCategory || { name: 'Category', slug: params.mainCategorySlug };
  const products = data.data || [];
  const categories = data.categories || [];
  
  const categoryNames = categories.map((cat: Category) => cat.name).join(', ');
  const productCount = data.pagination?.total || products.length;
  
  return {
    title: `${mainCategory.name} - Premium Organic Beauty Products | CocoJojo`,
    description: `Discover our ${mainCategory.name.toLowerCase()} collection with ${productCount} premium organic products. Shop ${categoryNames} and more with fast shipping.`,
    keywords: `${mainCategory.name.toLowerCase()}, organic beauty, natural skincare, ${categoryNames.toLowerCase()}, premium cosmetics`,
    openGraph: {
      title: `${mainCategory.name} - Premium Organic Beauty Products | CocoJojo`,
      description: `Discover our ${mainCategory.name.toLowerCase()} collection with ${productCount} premium organic products. Shop ${categoryNames} and more with fast shipping.`,
      type: "website",
      url: `/shop/${normalizedSlug}`,
      images: [
        {
          url: mainCategory.imageUrl || "/images/category-og.jpg",
          width: 1200,
          height: 630,
          alt: `${mainCategory.name} - CocoJojo`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: `${mainCategory.name} - Premium Organic Beauty Products | CocoJojo`,
      description: `Discover our ${mainCategory.name.toLowerCase()} collection with ${productCount} premium organic products.`,
    },
    alternates: {
      canonical: `/shop/${normalizedSlug}`,
    },
  };
}

export default async function MainCategoryPage({ params }: MainCategoryPageProps) {
  // Normalize the slug parameter
  const normalizedSlug = ensureValidSlug(params.mainCategorySlug);

  // Fetch data server-side for SEO
  const data = await getMainCategoryData(normalizedSlug);

  if (!data) {
    notFound();
  }

  return (
    <>
      {/* JSON-LD Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "CollectionPage",
            "name": data.mainCategory?.name || "Category",
            "description": `Premium organic ${data.mainCategory?.name?.toLowerCase() || 'beauty'} products`,
            "url": `https://cocojojo.com/shop/${normalizedSlug}`,
            "mainEntity": {
              "@type": "ItemList",
              "numberOfItems": data.pagination?.total || data.data?.length || 0,
              "itemListElement": (data.data || []).slice(0, 10).map((product: Product, index: number) => ({
                "@type": "Product",
                "position": index + 1,
                "name": product.name,
                "url": `https://cocojojo.com/shop/${params.mainCategorySlug}/product/${product.slug}`,
                "image": product.imageUrl,
                "offers": {
                  "@type": "Offer",
                  "price": product.salePrice || product.price,
                  "priceCurrency": "USD",
                  "availability": product.inStock ? "https://schema.org/InStock" : "https://schema.org/OutOfStock"
                }
              }))
            },
            "breadcrumb": {
              "@type": "BreadcrumbList",
              "itemListElement": [
                {
                  "@type": "ListItem",
                  "position": 1,
                  "name": "Home",
                  "item": "https://cocojojo.com"
                },
                {
                  "@type": "ListItem",
                  "position": 2,
                  "name": "Shop",
                  "item": "https://cocojojo.com/shop"
                },
                {
                  "@type": "ListItem",
                  "position": 3,
                  "name": data.mainCategory?.name || "Category",
                  "item": `https://cocojojo.com/shop/${normalizedSlug}`
                }
              ]
            }
          })
        }}
      />
      
      <MainCategoryPageClient
        initialData={data}
        mainCategorySlug={normalizedSlug}
      />
    </>
  );
}

// Enable ISR
export const revalidate = 86400; // 24 hours
