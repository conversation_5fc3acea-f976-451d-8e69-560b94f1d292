"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import Image from "next/image";
import Link from "next/link";
import { <PERSON><PERSON><PERSON>, FiList, FiChevronRight, FiShoppingBag } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import { createShopUrl, ensureValidSlug } from "@/utils/slugUtils";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl: string;
  price: number;
  salePrice: number | null;
  inStock: boolean;
  created_at: string;
  shortDescription?: string;
  tags?: string[];
}

interface CategoryData {
  category?: {
    id: number;
    name: string;
    slug: string;
    imageUrl: string;
  };
  data: Product[];
  pagination?: {
    total: number;
    page: number;
    limit: number;
  };
}

interface MainCategoryData {
  id: number;
  name: string;
  slug: string;
  imageUrl: string;
}

interface CategoryPageClientProps {
  initialData: CategoryData;
  mainCategoryData?: MainCategoryData;
  mainCategorySlug: string;
  categorySlug: string;
}

const CategoryPageClient = ({
  initialData,
  mainCategoryData,
  mainCategorySlug,
  categorySlug
}: CategoryPageClientProps) => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [sortBy, setSortBy] = useState<string>("featured");
  const [addingToCartId, setAddingToCartId] = useState<number | null>(null);
  const { addItem } = useCartStore();
  const router = useRouter();

  const { category, data: products, pagination } = initialData;

  const handleAddToCart = async (product: Product) => {
    if (!product.inStock) return;

    setAddingToCartId(product.id);

    try {
      await addItem({
        id: product.id.toString(),
        name: product.name,
        price: product.salePrice || product.price,
        image: product.imageUrl,
        sku: product.sku || `PROD-${product.id}`,
        stockQuantity: 100, // Default value
        stockStatus: product.inStock ? 'IN_STOCK' : 'OUT_OF_STOCK'
      }, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingToCartId(null);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <div
        className="relative h-64 md:h-80 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('${category?.imageUrl || 'https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80'}')`
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-4xl md:text-5xl font-bold mb-4">{category?.name || 'Category'}</h1>
            <p className="text-lg md:text-xl max-w-2xl mx-auto">
              Premium {category?.name?.toLowerCase() || 'beauty'} products for your daily routine
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Breadcrumb Navigation */}
        <nav className="flex items-center text-sm text-gray-600 mb-8">
          <Link href="/" className="hover:text-main-color">
            Home
          </Link>
          <FiChevronRight className="mx-2" />
          <Link href="/shop" className="hover:text-main-color">
            Shop
          </Link>
          <FiChevronRight className="mx-2" />
          <Link href={`/shop/${mainCategorySlug}`} className="hover:text-main-color">
            {mainCategoryData?.name || 'Category'}
          </Link>
          <FiChevronRight className="mx-2" />
          <span className="font-medium text-gray-800">{category?.name}</span>
        </nav>

        {/* Products Section */}
        {products && products.length > 0 && (
          <section>
            {/* Section Header */}
            <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
              <div>
                <h2 className="text-2xl font-bold text-gray-800 mb-2">
                  {category?.name} Products
                </h2>
                <p className="text-gray-600">
                  {pagination?.total || products.length} products available
                </p>
              </div>

              {/* Desktop Controls */}
              <div className="hidden md:flex items-center space-x-4 mt-4 md:mt-0">
                {/* View Mode Toggle */}
                <div className="flex items-center bg-white rounded-lg border p-1">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded ${
                      viewMode === "grid"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiGrid size={18} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 rounded ${
                      viewMode === "list"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiList size={18} />
                  </button>
                </div>

                {/* Sort Dropdown */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  className="bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
                >
                  <option value="featured">Sort by: Featured</option>
                  <option value="newest">Sort by: Newest</option>
                  <option value="price-low">Sort by: Price: Low to High</option>
                  <option value="price-high">Sort by: Price: High to Low</option>
                </select>
              </div>
            </div>

            {/* Mobile Controls */}
            <div className="md:hidden space-y-4 mb-6">
              {/* Mobile View Mode Toggle */}
              <div className="flex items-center justify-between">
                <div className="flex items-center bg-white rounded-lg border p-1">
                  <button
                    onClick={() => setViewMode("grid")}
                    className={`p-2 rounded ${
                      viewMode === "grid"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiGrid size={18} />
                  </button>
                  <button
                    onClick={() => setViewMode("list")}
                    className={`p-2 rounded ${
                      viewMode === "list"
                        ? "bg-main-color text-white"
                        : "text-gray-600 hover:text-main-color"
                    }`}
                  >
                    <FiList size={18} />
                  </button>
                </div>
              </div>

              {/* Mobile Sort */}
              <select
                value={sortBy}
                onChange={(e) => setSortBy(e.target.value)}
                className="w-full bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
              >
                <option value="featured">Sort by: Featured</option>
                <option value="newest">Sort by: Newest</option>
                <option value="price-low">Sort by: Price: Low to High</option>
                <option value="price-high">Sort by: Price: High to Low</option>
              </select>
            </div>

            {/* Products Display */}
            <div className={`grid gap-6 ${
              viewMode === "grid"
                ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
                : "grid-cols-1"
            }`}>
              {products.map((product) => (
                <div
                  key={product.id}
                  className={`group bg-white rounded-lg shadow-sm hover:shadow-md transition-shadow duration-300 ${
                    viewMode === "list" ? "flex" : ""
                  }`}
                >
                  <div className={`relative overflow-hidden ${
                    viewMode === "list"
                      ? "w-48 h-48 rounded-l-lg flex-shrink-0"
                      : "h-64 rounded-t-lg"
                  }`}>
                    <Link href={createShopUrl(ensureValidSlug(mainCategorySlug), ensureValidSlug(categorySlug), ensureValidSlug(product.slug))}>
                      <Image
                        src={product.imageUrl}
                        alt={product.name}
                        fill
                        className="object-cover transition-transform duration-500 group-hover:scale-105"
                      />
                    </Link>
                    {product.salePrice && (
                      <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-xs font-medium">
                        Sale
                      </div>
                    )}
                    {!product.inStock && (
                      <div className="absolute top-2 right-2 bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium">
                        Out of Stock
                      </div>
                    )}
                  </div>

                  <div className={`p-4 ${viewMode === "list" ? "flex-1 flex flex-col justify-between" : ""}`}>
                    <div>
                      <Link href={createShopUrl(ensureValidSlug(mainCategorySlug), ensureValidSlug(categorySlug), ensureValidSlug(product.slug))}>
                        <h3 className="font-medium text-gray-800 mb-2 line-clamp-2 hover:text-main-color transition-colors">
                          {product.name}
                        </h3>
                      </Link>
                      {product.shortDescription && (
                        <p className="text-sm text-gray-600 mb-3 line-clamp-2">{product.shortDescription}</p>
                      )}
                      {product.tags && product.tags.length > 0 && (
                        <div className="flex flex-wrap gap-1 mb-3">
                          {product.tags.slice(0, 3).map((tag, index) => (
                            <span key={index} className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                              {tag}
                            </span>
                          ))}
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        {product.salePrice ? (
                          <>
                            <span className="text-lg font-bold text-main-color">${product.salePrice}</span>
                            <span className="text-sm text-gray-500 line-through">${product.price}</span>
                          </>
                        ) : (
                          <span className="text-lg font-bold text-gray-800">${product.price}</span>
                        )}
                      </div>

                      <button
                        onClick={() => handleAddToCart(product)}
                        disabled={!product.inStock || addingToCartId === product.id}
                        className={`flex items-center space-x-1 px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                          product.inStock
                            ? "bg-main-color text-white hover:bg-main-color/90"
                            : "bg-gray-300 text-gray-500 cursor-not-allowed"
                        }`}
                      >
                        <FiShoppingBag size={16} />
                        <span>
                          {addingToCartId === product.id
                            ? "Adding..."
                            : product.inStock
                              ? "Add to Cart"
                              : "Out of Stock"
                          }
                        </span>
                      </button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </section>
        )}

        {/* Empty State */}
        {(!products || products.length === 0) && (
          <div className="text-center py-16">
            <h3 className="text-xl font-medium text-gray-800 mb-2">No products found</h3>
            <p className="text-gray-600 mb-6">We couldn't find any products in this category.</p>
            <Link
              href="/shop"
              className="inline-flex items-center px-6 py-3 bg-main-color text-white rounded-lg hover:bg-main-color/90 transition-colors"
            >
              Browse All Products
            </Link>
          </div>
        )}
      </div>
    </div>
  );
};

export default CategoryPageClient;
