"use client";

import { useState } from "react";
import Link from "next/link";
import { FiChevronRight, FiShoppingBag, FiHeart, FiShare2, FiStar } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import ProductImageGallery from "@/components/shop/product/ProductImageGallery";

// Types
interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  imageUrl?: string;
  price: number;
  salePrice: number | null;
  inStock?: boolean;
  stockStatus?: string;
  created_at?: string;
  createdAt?: string;
  shortDescription?: string;
  description?: string;
  tags?: Array<{
    id: number;
    name: string;
    slug: string;
  }>;
  images?: Array<{
    id: number;
    url: string;
    position: number;
  }>;
  categories?: Array<{
    id: number;
    name: string;
    slug: string;
    imageUrl: string;
  }>;
  listings?: Array<{
    id: number;
    title: string;
    content: string;
  }>;
  variants?: Array<{
    id: number;
    sku: string;
    price: number;
    stockQuantity: number;
    stockStatus: string;
    ProductImage?: Array<{
      id: number;
      url: string;
      position: number;
    }>;
    attributes?: Array<{
      value: {
        id: number;
        value: string;
      };
    }>;
  }>;
  specifications?: Record<string, any>;
}

interface ProductDetailPageClientProps {
  initialProduct: Product;
  productSlug: string;
}

const ProductDetailPageClient = ({
  initialProduct,
  productSlug
}: ProductDetailPageClientProps) => {
  const [quantity, setQuantity] = useState(1);
  const [addingToCart, setAddingToCart] = useState(false);
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews'>('description');
  const { addItem } = useCartStore();

  const product = initialProduct;
  const currentPrice = product.salePrice || product.price;
  const originalPrice = product.price;
  const hasDiscount = product.salePrice && product.salePrice < product.price;

  // Debug: Log product data to understand the structure
  console.log('ProductDetailPageClient - Product data:', product);
  console.log('ProductDetailPageClient - Product imageUrl type:', typeof product.imageUrl);
  console.log('ProductDetailPageClient - Product images:', product.images);

  // Handle the actual API response structure
  const productImages = product.images || [];
  const productCategories = product.categories || [];
  const productTags = product.tags || [];
  const productListings = product.listings || [];
  const productVariants = product.variants || [];

  const handleAddToCart = async () => {
    const isInStock = product.inStock || product.stockStatus === 'IN_STOCK';
    if (!isInStock) return;

    setAddingToCart(true);

    try {
      // Get the first image URL from the images array or use a fallback
      const imageUrl = product.imageUrl ||
                      (product.images && product.images.length > 0 ? product.images[0].url : '') ||
                      '/images/placeholder-product.jpg';

      await addItem({
        id: product.id.toString(),
        name: product.name,
        price: currentPrice,
        image: imageUrl,
        sku: product.sku || `PROD-${product.id}`,
        stockQuantity: 100, // Default value
        stockStatus: isInStock ? 'IN_STOCK' : 'OUT_OF_STOCK',
        variantId: null
      }, quantity);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setAddingToCart(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: product.shortDescription,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb Navigation */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-6 border-b">
        <nav className="flex items-center text-sm text-gray-600">
          <Link href="/" className="hover:text-main-color">
            Home
          </Link>
          <FiChevronRight className="mx-2" />
          <Link href="/shop" className="hover:text-main-color">
            Shop
          </Link>
          <FiChevronRight className="mx-2" />
          <span className="font-medium text-gray-800">{product.name}</span>
        </nav>
      </div>

      {/* Product Details */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div>
            <ProductImageGallery
              images={(() => {
                const allImages = [];

                // Add main image if it exists
                if (product.imageUrl) {
                  allImages.push({
                    id: 0,
                    url: product.imageUrl,
                    position: 0
                  });
                }

                // Add additional images from the images array
                if (product.images && Array.isArray(product.images)) {
                  product.images.forEach((img, index) => {
                    if (img && typeof img === 'object' && img.url) {
                      allImages.push({
                        id: img.id || index + 1,
                        url: img.url,
                        position: img.position || index + 1
                      });
                    }
                  });
                }

                return allImages;
              })()}
            />
          </div>

          {/* Product Information */}
          <div>
            <div className="sticky top-24">
              {/* Product Title and Price */}
              <div className="mb-6">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <FiStar key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="ml-2 text-sm text-gray-600">(127 reviews)</span>
                </div>

                {/* Price */}
                <div className="flex items-center space-x-3 mb-4">
                  <span className="text-3xl font-bold text-main-color">${currentPrice}</span>
                  {hasDiscount && (
                    <>
                      <span className="text-xl text-gray-500 line-through">${originalPrice}</span>
                      <span className="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
                        Save ${(originalPrice - currentPrice).toFixed(2)}
                      </span>
                    </>
                  )}
                </div>

                {/* Stock Status */}
                <div className="mb-6">
                  {product.inStock ? (
                    <span className="text-green-600 font-medium">✓ In Stock</span>
                  ) : (
                    <span className="text-red-600 font-medium">✗ Out of Stock</span>
                  )}
                </div>

                {/* Short Description */}
                {product.shortDescription && (
                  <p className="text-gray-600 mb-6">{product.shortDescription}</p>
                )}

                {/* Tags */}
                {product.tags && product.tags.length > 0 && (
                  <div className="flex flex-wrap gap-2 mb-6">
                    {productTags.map((tag, index) => (
                      <span key={index} className="bg-gray-100 text-gray-700 text-sm px-3 py-1 rounded-full">
                        {typeof tag === 'string' ? tag : tag.name}
                      </span>
                    ))}
                  </div>
                )}
              </div>

              {/* Variant Selection */}
              {product.variants && product.variants.length > 0 && (
                <div className="mb-6">
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-800">Product Options</h3>
                    {/* Simple variant display - can be enhanced later */}
                    <div className="text-sm text-gray-600">
                      {product.variants.length} variant{product.variants.length > 1 ? 's' : ''} available
                    </div>
                  </div>
                </div>
              )}

              {/* Quantity and Add to Cart */}
              <div className="mb-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center border rounded-lg">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-2 text-gray-600 hover:text-gray-800"
                    >
                      -
                    </button>
                    <span className="px-4 py-2 border-x">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="px-3 py-2 text-gray-600 hover:text-gray-800"
                    >
                      +
                    </button>
                  </div>

                  <button
                    onClick={handleAddToCart}
                    disabled={!product.inStock || addingToCart}
                    className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors ${
                      product.inStock
                        ? "bg-main-color text-white hover:bg-main-color/90"
                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                    }`}
                  >
                    <FiShoppingBag size={20} />
                    <span>
                      {addingToCart
                        ? "Adding to Cart..."
                        : product.inStock
                          ? "Add to Cart"
                          : "Out of Stock"
                      }
                    </span>
                  </button>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <FiHeart size={18} />
                    <span>Add to Wishlist</span>
                  </button>
                  <button
                    onClick={handleShare}
                    className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FiShare2 size={18} />
                    <span>Share</span>
                  </button>
                </div>
              </div>

              {/* Product SKU */}
              <div className="text-sm text-gray-600">
                SKU: {product.sku}
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {['description', 'specifications', 'reviews'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                    activeTab === tab
                      ? 'border-main-color text-main-color'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                {product.description ? (
                  <div dangerouslySetInnerHTML={{ __html: product.description }} />
                ) : (
                  <p>No description available for this product.</p>
                )}
              </div>
            )}

            {activeTab === 'specifications' && (
              <div>
                {product.specifications ? (
                  <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(product.specifications).map(([key, value]) => (
                      <div key={key} className="border-b border-gray-200 pb-2">
                        <dt className="font-medium text-gray-900 capitalize">{key.replace('_', ' ')}</dt>
                        <dd className="text-gray-600">{String(value)}</dd>
                      </div>
                    ))}
                  </dl>
                ) : (
                  <p>No specifications available for this product.</p>
                )}
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <p className="text-gray-600">Reviews feature coming soon...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPageClient;
