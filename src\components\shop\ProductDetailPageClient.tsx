"use client";

import { useState, useEffect } from "react";
import Link from "next/link";
import { FiChevronRight, FiShoppingBag, FiHeart, FiShare2, FiStar, FiInfo, FiList, FiLock, FiEyeOff } from "react-icons/fi";
import { useCartStore } from "@/hooks/useCartStore";
import ProductImageGallery from "@/components/shop/product/ProductImageGallery";
import ProductVariantSelector from "@/components/shop/product/ProductVariantSelector";
import ProductListings from "@/components/shop/product/ProductListings";
import ProductReviews from "@/components/shop/product/ProductReviews";

// Types based on the API response
interface ProductImage {
  id: number;
  url: string;
  position: number;
}

interface ProductCategory {
  id: number;
  name: string;
  slug: string;
  imageUrl?: string;
}

interface ProductTag {
  id: number;
  name: string;
  slug: string;
}

interface ProductListing {
  id: number;
  title: string;
  content: string;
}

interface AttributeValue {
  id: number;
  value: string;
}

interface Attribute {
  id: number;
  name: string;
  values: AttributeValue[];
}

interface ProductAttribute {
  attribute: Attribute;
  values: AttributeValue[];
}

interface VariantAttribute {
  value: AttributeValue;
}

interface ProductVariant {
  id: number;
  sku: string;
  price: number;
  stockQuantity: number;
  stockStatus: string;
  ProductImage: ProductImage[];
  attributes: VariantAttribute[];
}

interface Product {
  id: number;
  sku: string;
  name: string;
  slug: string;
  description: string;
  shortDescription: string;
  price: number;
  salePrice: number | null;
  stockQuantity: number;
  stockStatus: string;
  type: string;
  access: string;
  createdAt: string;
  updatedAt: string;
  images: ProductImage[];
  categories: ProductCategory[];
  tags: ProductTag[];
  listings: ProductListing[];
  ProductAttribute?: ProductAttribute[];
  variants?: ProductVariant[];
}

interface ProductDetailPageClientProps {
  initialProduct: Product;
  productSlug: string;
}

const ProductDetailPageClient = ({
  initialProduct,
  productSlug
}: ProductDetailPageClientProps) => {
  const [selectedVariant, setSelectedVariant] = useState<ProductVariant | null>(null);
  const [selectedAttributes, setSelectedAttributes] = useState<Record<string, AttributeValue>>({});
  const [isAddingToCart, setIsAddingToCart] = useState(false);
  const [quantity, setQuantity] = useState(1);
  const [activeTab, setActiveTab] = useState<'description' | 'specifications' | 'reviews'>('description');
  const [isWishlisted, setIsWishlisted] = useState(false);
  const { addItem } = useCartStore();

  const product = initialProduct;

  // Initialize selected variant for grouped products
  useEffect(() => {
    if (product.type === 'GROUPED' && product.variants && product.variants.length > 0) {
      // Find a variant with images if possible
      const variantWithImages = product.variants.find((v: ProductVariant) =>
        v.ProductImage && v.ProductImage.length > 0
      );
      const initialVariant = variantWithImages || product.variants[0];
      setSelectedVariant(initialVariant);

      // Initialize selected attributes based on the selected variant
      if (initialVariant.attributes) {
        const initialAttributes: Record<string, AttributeValue> = {};
        initialVariant.attributes.forEach((attr: VariantAttribute) => {
          const attributeInfo = product.ProductAttribute?.find((pa: any) =>
            pa.values.some((v: any) => v.id === attr.value.id)
          );
          const attributeId = attributeInfo?.attribute.id;
          if (attributeId) {
            initialAttributes[attributeId.toString()] = attr.value;
          }
        });
        setSelectedAttributes(initialAttributes);
      }
    }
  }, [product]);

  // Handle attribute selection for grouped products
  const handleAttributeChange = (attributeId: number, value: AttributeValue) => {
    const newSelectedAttributes = {
      ...selectedAttributes,
      [attributeId.toString()]: value
    };
    setSelectedAttributes(newSelectedAttributes);

    // Find the matching variant based on selected attributes
    if (product?.variants) {
      const matchingVariant = findMatchingVariant(product.variants, newSelectedAttributes);
      if (matchingVariant) {
        setSelectedVariant(matchingVariant);
      }
    }
  };

  // Find a variant that matches the selected attributes
  const findMatchingVariant = (variants: ProductVariant[], selectedAttrs: Record<string, AttributeValue>) => {
    // First try to find an exact match (all attributes match)
    const exactMatch = variants.find(variant => {
      const allAttributesMatch = Object.entries(selectedAttrs).every(([_, attrValue]) => {
        return variant.attributes.some(va => va.value.id === attrValue.id);
      });
      const sameAttributeCount = variant.attributes.length === Object.keys(selectedAttrs).length;
      return allAttributesMatch && sameAttributeCount;
    });

    if (exactMatch) {
      return exactMatch;
    }

    // If no exact match, find the best partial match
    return variants.find(variant => {
      return Object.entries(selectedAttrs).every(([_, attrValue]) => {
        return variant.attributes.some(va => va.value.id === attrValue.id);
      });
    });
  };

  // Determine if the product is new (created within the last week)
  const isNewProduct = (createdAt: string) => {
    const createdDate = new Date(createdAt);
    const now = new Date();
    const oneWeekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
    return createdDate > oneWeekAgo;
  };

  // Determine the current price (accounting for sale price and variants)
  const currentPrice = selectedVariant
    ? selectedVariant.price
    : product.salePrice !== null
      ? product.salePrice
      : product.price;

  // Determine the original price for comparison
  const originalPrice = selectedVariant
    ? (product.price > selectedVariant.price ? product.price : null)
    : product.salePrice !== null
      ? product.price
      : null;

  const handleAddToCart = async () => {
    const isInStock = (selectedVariant ? selectedVariant.stockStatus : product.stockStatus) === 'IN_STOCK';
    if (!isInStock) return;

    setIsAddingToCart(true);

    try {
      const variant = selectedVariant;
      const productToAdd = {
        id: variant ? variant.id : product.id,
        name: product.name,
        price: variant ? variant.price : (product.salePrice !== null ? product.salePrice : product.price),
        image: variant && variant.ProductImage && variant.ProductImage.length > 0
          ? variant.ProductImage[0].url
          : (product.images && product.images.length > 0 ? product.images[0].url : ''),
        sku: variant ? variant.sku : product.sku,
        stockQuantity: variant ? variant.stockQuantity : product.stockQuantity,
        stockStatus: variant ? variant.stockStatus : product.stockStatus,
        variantId: variant ? variant.id : null
      };

      addItem(productToAdd, quantity);

      // Set a timeout to reset the loading state
      setTimeout(() => {
        setIsAddingToCart(false);
      }, 800);
    } catch (error) {
      console.error('Error adding to cart:', error);
      setIsAddingToCart(false);
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: product.name,
          text: product.shortDescription,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
    }
  };

  const handleWishlist = () => {
    setIsWishlisted(!isWishlisted);
    // Here you would typically save to wishlist API
  };

  // Calculate discount
  const hasDiscount = originalPrice && originalPrice > currentPrice;

  return (
    <div className="min-h-screen bg-white">
      {/* Breadcrumb Navigation */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-6 border-b">
        <nav className="flex items-center text-sm text-gray-600">
          <Link href="/" className="hover:text-main-color">
            Home
          </Link>
          <FiChevronRight className="mx-2" />
          <Link href="/shop" className="hover:text-main-color">
            Shop
          </Link>
          <FiChevronRight className="mx-2" />
          <span className="font-medium text-gray-800">{product.name}</span>
        </nav>
      </div>

      {/* Product Details */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Product Images */}
          <div>
            <ProductImageGallery
              images={selectedVariant?.ProductImage?.length
                ? selectedVariant.ProductImage
                : (product.images?.length ? product.images : [])
              }
            />
          </div>

          {/* Product Information */}
          <div>
            <div className="sticky top-24">
              {/* Product Title and Price */}
              <div className="mb-6">
                <h1 className="text-3xl font-bold text-gray-900 mb-4">{product.name}</h1>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  <div className="flex items-center">
                    {[...Array(5)].map((_, i) => (
                      <FiStar key={i} className="w-5 h-5 text-yellow-400 fill-current" />
                    ))}
                  </div>
                  <span className="ml-2 text-sm text-gray-600">(127 reviews)</span>
                </div>

                {/* Price */}
                <div className="flex items-center space-x-3 mb-4">
                  <span className="text-3xl font-bold text-main-color">${currentPrice}</span>
                  {hasDiscount && (
                    <>
                      <span className="text-xl text-gray-500 line-through">${originalPrice}</span>
                      <span className="bg-red-100 text-red-800 text-sm font-medium px-2 py-1 rounded">
                        Save ${(originalPrice - currentPrice).toFixed(2)}
                      </span>
                    </>
                  )}
                </div>

                {/* Stock Status */}
                <div className="mb-6">
                  {(selectedVariant ? selectedVariant.stockStatus : product.stockStatus) === 'IN_STOCK' ? (
                    <span className="text-green-600 font-medium">✓ In Stock</span>
                  ) : (
                    <span className="text-red-600 font-medium">✗ Out of Stock</span>
                  )}

                  {/* Show stock quantity if low */}
                  {(selectedVariant?.stockQuantity || product.stockQuantity) > 0 &&
                   (selectedVariant?.stockQuantity || product.stockQuantity) <= 2 && (
                    <div className="text-sm text-red-600 mt-1 font-medium">
                      Only {selectedVariant ? selectedVariant.stockQuantity : product.stockQuantity} left in stock!
                    </div>
                  )}
                </div>

                {/* Short Description */}
                {product.shortDescription && (
                  <div
                    className="text-gray-600 mb-6 prose prose-sm max-w-none"
                    dangerouslySetInnerHTML={{ __html: product.shortDescription }}
                  />
                )}


              </div>

              {/* Variant Selection */}
              {product.variants && product.variants.length > 0 && (
                <div className="mb-6">
                  <div className="space-y-4">
                    <h3 className="font-medium text-gray-800">Product Options</h3>
                    {/* Simple variant display - can be enhanced later */}
                    <div className="text-sm text-gray-600">
                      {product.variants.length} variant{product.variants.length > 1 ? 's' : ''} available
                    </div>
                  </div>
                </div>
              )}

              {/* Quantity and Add to Cart */}
              <div className="mb-6">
                <div className="flex items-center space-x-4 mb-4">
                  <div className="flex items-center border rounded-lg">
                    <button
                      onClick={() => setQuantity(Math.max(1, quantity - 1))}
                      className="px-3 py-2 text-gray-600 hover:text-gray-800"
                    >
                      -
                    </button>
                    <span className="px-4 py-2 border-x">{quantity}</span>
                    <button
                      onClick={() => setQuantity(quantity + 1)}
                      className="px-3 py-2 text-gray-600 hover:text-gray-800"
                    >
                      +
                    </button>
                  </div>

                  <button
                    onClick={handleAddToCart}
                    disabled={(selectedVariant ? selectedVariant.stockStatus : product.stockStatus) !== 'IN_STOCK' || isAddingToCart}
                    className={`flex-1 flex items-center justify-center space-x-2 px-6 py-3 rounded-lg font-medium transition-colors ${
                      (selectedVariant ? selectedVariant.stockStatus : product.stockStatus) === 'IN_STOCK'
                        ? "bg-main-color text-white hover:bg-main-color/90"
                        : "bg-gray-300 text-gray-500 cursor-not-allowed"
                    }`}
                  >
                    <FiShoppingBag size={20} />
                    <span>
                      {isAddingToCart
                        ? "Adding to Cart..."
                        : (selectedVariant ? selectedVariant.stockStatus : product.stockStatus) === 'IN_STOCK'
                          ? "Add to Cart"
                          : "Out of Stock"
                      }
                    </span>
                  </button>
                </div>

                {/* Action Buttons */}
                <div className="flex space-x-3">
                  <button className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors">
                    <FiHeart size={18} />
                    <span>Add to Wishlist</span>
                  </button>
                  <button
                    onClick={handleShare}
                    className="flex items-center space-x-2 px-4 py-2 border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <FiShare2 size={18} />
                    <span>Share</span>
                  </button>
                </div>
              </div>

              {/* Product SKU */}
              <div className="text-sm text-gray-600">
                SKU: {product.sku}
              </div>
            </div>
          </div>
        </div>

        {/* Product Details Tabs */}
        <div className="mt-16">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8">
              {['description', 'specifications', 'reviews'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab as any)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                    activeTab === tab
                      ? 'border-main-color text-main-color'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab}
                </button>
              ))}
            </nav>
          </div>

          <div className="py-8">
            {activeTab === 'description' && (
              <div className="prose max-w-none">
                {product.description ? (
                  <div dangerouslySetInnerHTML={{ __html: product.description }} />
                ) : (
                  <p>No description available for this product.</p>
                )}
              </div>
            )}

            {activeTab === 'specifications' && (
              <div>
                <p className="text-gray-600">Specifications will be available soon.</p>
              </div>
            )}

            {activeTab === 'reviews' && (
              <div>
                <p className="text-gray-600">Reviews feature coming soon...</p>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ProductDetailPageClient;
