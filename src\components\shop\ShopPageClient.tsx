"use client";

import { useState, useEffect } from "react";
import { FiFilter, FiX, FiChevronDown, FiGrid, FiList } from "react-icons/fi";
import CategoryFilter from "@/components/shop/CategoryFilter";
import ProductGrid from "@/components/shop/ProductGrid";
import ProductList from "@/components/shop/ProductList";
import FeaturedProducts from "@/components/shop/FeaturedProducts";
import CategoryHierarchy from "@/components/shop/CategoryHierarchy";
import { categories, loadCategoriesWithSubcategories } from "@/data/hierarchicalCategories";

interface ShopPageClientProps {
  initialCategories?: any[];
}

const ShopPageClient = ({ initialCategories = categories }: ShopPageClientProps) => {
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<number | null>(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState<number | null>(null);
  // Price range and sort options for filtering
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 100]);
  const [sortBy, setSortBy] = useState<string>("featured");
  const [isLoading, setIsLoading] = useState(true);
  const [categoriesData, setCategoriesData] = useState(initialCategories);

  // Load categories from API when component mounts
  useEffect(() => {
    const fetchCategories = async () => {
      setIsLoading(true);
      try {
        const data = await loadCategoriesWithSubcategories();
        setCategoriesData(data);
      } catch (error) {
        console.error('Error loading categories:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchCategories();
  }, []);

  // Reset subcategory when category changes
  const handleCategorySelect = (categoryId: number | null) => {
    setSelectedCategory(categoryId);
    setSelectedSubcategory(null);
  };

  // Handle subcategory selection
  const handleSubcategorySelect = (subcategoryId: number | null) => {
    setSelectedSubcategory(subcategoryId);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Hero Section with Background Image */}
      <div 
        className="relative h-96 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: "url('https://images.unsplash.com/photo-1596462502278-27bfdc403348?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2340&q=80')"
        }}
      >
        <div className="absolute inset-0 bg-black bg-opacity-40"></div>
        <div className="relative z-10 flex items-center justify-center h-full">
          <div className="text-center text-white">
            <h1 className="text-5xl font-bold mb-4">Shop Our Collection</h1>
            <p className="text-xl max-w-2xl mx-auto">
              Discover premium organic beauty products crafted with pure, natural ingredients for real results and well-being.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="px-4 md:px-8 lg:px-16 xl:px-32 2xl:px-64 py-12">
        {/* Loading State */}
        {isLoading ? (
          <div className="flex justify-center items-center h-64">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-main-color"></div>
          </div>
        ) : (
          <>
            {/* Category Hierarchy Navigation */}
            <section className="mb-16">
              <CategoryHierarchy
                categories={categoriesData}
                selectedCategory={selectedCategory}
                selectedSubcategory={selectedSubcategory}
                onSelectCategory={handleCategorySelect}
                onSelectSubcategory={handleSubcategorySelect}
              />
            </section>

            {/* Featured Products Section - Only show when no category is selected */}
            {!selectedCategory && !selectedSubcategory && (
              <section className="mb-16">
                <div className="text-center mb-12">
                  <h2 className="text-3xl font-bold text-gray-800 mb-4">Featured Products</h2>
                  <p className="text-gray-600 max-w-2xl mx-auto">
                    Discover our most popular and highly-rated products, carefully selected for their exceptional quality and customer satisfaction.
                  </p>
                </div>
                <FeaturedProducts />
              </section>
            )}

            {/* Products Section */}
            {(selectedCategory || selectedSubcategory) && (
              <section>
                {/* Section Header */}
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-8">
                  <div>
                    <h2 className="text-2xl font-bold text-gray-800 mb-2">
                      {selectedSubcategory 
                        ? categoriesData.find(cat => cat.id === selectedCategory)?.subcategories.find(sub => sub.id === selectedSubcategory)?.name
                        : categoriesData.find(cat => cat.id === selectedCategory)?.name
                      } Products
                    </h2>
                    <p className="text-gray-600">
                      Browse our curated selection of premium products
                    </p>
                  </div>

                  {/* Desktop Controls */}
                  <div className="hidden md:flex items-center space-x-4">
                    {/* View Mode Toggle */}
                    <div className="flex items-center bg-white rounded-lg border p-1">
                      <button
                        onClick={() => setViewMode("grid")}
                        className={`p-2 rounded ${
                          viewMode === "grid"
                            ? "bg-main-color text-white"
                            : "text-gray-600 hover:text-main-color"
                        }`}
                      >
                        <FiGrid size={18} />
                      </button>
                      <button
                        onClick={() => setViewMode("list")}
                        className={`p-2 rounded ${
                          viewMode === "list"
                            ? "bg-main-color text-white"
                            : "text-gray-600 hover:text-main-color"
                        }`}
                      >
                        <FiList size={18} />
                      </button>
                    </div>

                    {/* Sort Dropdown */}
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
                    >
                      <option value="featured">Sort by: Featured</option>
                      <option value="newest">Sort by: Newest</option>
                      <option value="price-low">Sort by: Price: Low to High</option>
                      <option value="price-high">Sort by: Price: High to Low</option>
                    </select>

                    {/* Filter Toggle */}
                    <button
                      onClick={() => setShowFilters(!showFilters)}
                      className="flex items-center space-x-2 bg-white border rounded-lg px-4 py-2 text-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-main-color"
                    >
                      <FiFilter size={16} />
                      <span>Filters</span>
                      <FiChevronDown
                        size={16}
                        className={`transform transition-transform ${
                          showFilters ? "rotate-180" : ""
                        }`}
                      />
                    </button>
                  </div>
                </div>

                {/* Mobile Controls */}
                <div className="md:hidden space-y-4 mb-6">
                  {/* Mobile View Mode Toggle */}
                  <div className="flex items-center justify-between">
                    <div className="flex items-center bg-white rounded-lg border p-1">
                      <button
                        onClick={() => setViewMode("grid")}
                        className={`p-2 rounded ${
                          viewMode === "grid"
                            ? "bg-main-color text-white"
                            : "text-gray-600 hover:text-main-color"
                        }`}
                      >
                        <FiGrid size={18} />
                      </button>
                      <button
                        onClick={() => setViewMode("list")}
                        className={`p-2 rounded ${
                          viewMode === "list"
                            ? "bg-main-color text-white"
                            : "text-gray-600 hover:text-main-color"
                        }`}
                      >
                        <FiList size={18} />
                      </button>
                    </div>

                    {/* Mobile Filter Toggle */}
                    <button
                      onClick={() => setShowFilters(!showFilters)}
                      className="flex items-center space-x-2 bg-white border rounded-lg px-4 py-2 text-sm hover:bg-gray-50"
                    >
                      <FiFilter size={16} />
                      <span>Filters</span>
                    </button>
                  </div>

                  {/* Mobile Sort */}
                  <div className="md:hidden mb-6">
                    <select
                      value={sortBy}
                      onChange={(e) => setSortBy(e.target.value)}
                      className="w-full bg-white border rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-main-color"
                    >
                      <option value="featured">Sort by: Featured</option>
                      <option value="newest">Sort by: Newest</option>
                      <option value="price-low">Sort by: Price: Low to High</option>
                      <option value="price-high">Sort by: Price: High to Low</option>
                    </select>
                  </div>

                  {/* Products Display */}
                  {viewMode === "grid" ? (
                    <ProductGrid
                      selectedCategory={selectedCategory}
                      selectedSubcategory={selectedSubcategory}
                    />
                  ) : (
                    <ProductList
                      selectedCategory={selectedCategory}
                      selectedSubcategory={selectedSubcategory}
                    />
                  )}
                </div>

                {/* Filters Sidebar */}
                {showFilters && (
                  <div className="mb-8">
                    <div className="bg-white rounded-lg shadow-sm border p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-medium text-gray-800">Filters</h3>
                        <button
                          onClick={() => setShowFilters(false)}
                          className="text-gray-500 hover:text-gray-700"
                        >
                          <FiX size={20} />
                        </button>
                      </div>
                      <CategoryFilter
                        priceRange={priceRange}
                        onPriceRangeChange={setPriceRange}
                        selectedCategory={selectedCategory}
                        onCategoryChange={handleCategorySelect}
                      />
                    </div>
                  </div>
                )}

                {/* Products Display - Desktop */}
                <div className="hidden md:block">
                  {viewMode === "grid" ? (
                    <ProductGrid
                      selectedCategory={selectedCategory}
                      selectedSubcategory={selectedSubcategory}
                    />
                  ) : (
                    <ProductList
                      selectedCategory={selectedCategory}
                      selectedSubcategory={selectedSubcategory}
                    />
                  )}
                </div>
              </section>
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default ShopPageClient;
