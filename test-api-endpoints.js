// Test script to verify API endpoints work correctly
const API_BASE_URL = 'https://coco-jojo-api-production.up.railway.app';

async function testEndpoints() {
  console.log('🧪 Testing API Endpoints...\n');

  // Test 1: Main categories
  try {
    console.log('1. Testing main categories endpoint...');
    const response = await fetch(`${API_BASE_URL}/api/shop/main-categories`);
    const data = await response.json();
    console.log(`✅ Main categories: ${data.data?.length || 0} found`);
    
    if (data.data && data.data.length > 0) {
      const firstCategory = data.data[0];
      console.log(`   First category: ${firstCategory.name} (${firstCategory.slug})`);
      
      // Test 2: Main category products
      console.log(`\n2. Testing main category products for "${firstCategory.slug}"...`);
      const categoryResponse = await fetch(`${API_BASE_URL}/api/shop/main-categories/slug/${firstCategory.slug}/products?page=1&limit=5`);
      const categoryData = await categoryResponse.json();
      console.log(`✅ Products in ${firstCategory.name}: ${categoryData.pagination?.total || categoryData.data?.length || 0}`);
      console.log(`   Categories: ${categoryData.categories?.length || 0}`);
      
      if (categoryData.categories && categoryData.categories.length > 0) {
        const firstSubCategory = categoryData.categories[0];
        console.log(`   First subcategory: ${firstSubCategory.name} (${firstSubCategory.slug})`);
        
        // Test 3: Subcategory products
        console.log(`\n3. Testing subcategory products for "${firstSubCategory.slug}"...`);
        const subCategoryResponse = await fetch(`${API_BASE_URL}/api/shop/categories/slug/${firstSubCategory.slug}/products?page=1&limit=5`);
        const subCategoryData = await subCategoryResponse.json();
        console.log(`✅ Products in ${firstSubCategory.name}: ${subCategoryData.pagination?.total || subCategoryData.data?.length || 0}`);
        
        if (subCategoryData.data && subCategoryData.data.length > 0) {
          const firstProduct = subCategoryData.data[0];
          console.log(`   First product: ${firstProduct.name} (${firstProduct.slug})`);
          
          // Test 4: Single product
          console.log(`\n4. Testing single product for "${firstProduct.slug}"...`);
          const productResponse = await fetch(`${API_BASE_URL}/api/shop/products/slug/${firstProduct.slug}`);
          const productData = await productResponse.json();
          console.log(`✅ Product details: ${productData.data?.name || productData.name || 'Found'}`);
        }
      }
    }
  } catch (error) {
    console.error('❌ Error testing endpoints:', error.message);
  }

  console.log('\n🎉 API endpoint testing complete!');
}

// Test specific endpoint that was failing
async function testSpecificEndpoint() {
  console.log('\n🔍 Testing specific endpoint that was failing...');
  
  try {
    const response = await fetch(`${API_BASE_URL}/api/shop/essential-oil?page=1&limit=20`);
    console.log(`Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ Essential oil endpoint works!');
      console.log(`   Products found: ${data.pagination?.total || data.data?.length || 0}`);
    } else {
      console.log('❌ Essential oil endpoint failed');
      console.log(`   Error: ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ Error testing essential oil endpoint:', error.message);
  }
}

// Run tests
testEndpoints().then(() => testSpecificEndpoint());
